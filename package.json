{"name": "coconut-api", "version": "1.0.0", "description": "Filing service", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "rimraf dist && tsc", "start": "node dist/server.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.750.0", "@google-cloud/storage": "^7.16.0", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-timeout-handler": "^2.2.2", "helmet": "^7.1.0", "joi": "^17.7.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.6.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "redis": "^4.6.10", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.4.3", "zod": "^3.21.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.3", "@types/cors": "^2.8.14", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.2", "@types/mongoose": "^5.11.97", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^20.11.21", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.7", "nodemon": "^3.1.0", "rimraf": "^5.0.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.4.2"}}